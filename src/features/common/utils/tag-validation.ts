/**
 * Validates tag input and sets appropriate validation messages
 * @param input The input element to validate
 * @param maxTags Maximum number of tags allowed (default: 5)
 * @param minTags Minimum number of tags required (default: 0)
 */
export function validateTagsInput(
  input: HTMLInputElement | { value: string; setCustomValidity: (message: string) => void },
  maxTags: number = 5,
  minTags: number = 0
): void {
  const tags = input.value.split(/\s+/).filter((tag) => tag.length > 0);

  if (tags.length > maxTags) {
    input.setCustomValidity(`Maximum ${maxTags} tags allowed`);
  } else if (tags.length < minTags && input.value.trim().length > 0) {
    input.setCustomValidity(`Minimum ${minTags} tags required`);
  } else {
    input.setCustomValidity('');
  }
}

/**
 * Parses tags from input string
 * @param input The input string containing tags
 * @returns Array of cleaned tag strings
 */
export function parseTags(input: string): string[] {
  return input.split(/\s+/).filter((tag) => tag.length > 0);
}

/**
 * Validates if tags array meets requirements
 * @param tags Array of tag strings
 * @param maxTags Maximum number of tags allowed
 * @param minTags Minimum number of tags required
 * @returns Validation result with error message if invalid
 */
export function validateTagsArray(
  tags: string[],
  maxTags: number = 5,
  minTags: number = 0
): { isValid: boolean; error?: string } {
  if (tags.length > maxTags) {
    return { isValid: false, error: `Maximum ${maxTags} tags allowed` };
  }
  if (tags.length < minTags) {
    return { isValid: false, error: `Minimum ${minTags} tags required` };
  }
  return { isValid: true };
}
